I want to make a web app which collects and stores college information to help students find colleges.

I want the landing page to have different boxes with different counselling's like josaa/csab   
jac delhi comedk/kcet mhtcet wbjee   
in each counselling we will list the colleges in hierarchical order   
when you click on the respective college again different blocks like academics, hostels, fests , clubs, placements   
we will take out most of the data from their respective college sites and the rest will be fed in by the students of that college 

in academics we will have different departments within that department we will put all the info regarding the profs (taken from the website) existing students can add points regarding those profs if they are chill, their checking how lenient they are regarding attendance etc, we could rate them as well, apart from that all the labs in that department, existing industry collaborations, syllabus(taken from the site), different accreditations of that department, research papers etc, unofficial placements compiled by seniors will be attached along with the nirf report of that college

hostels will have all the major hostel and pg accommodation available for students , all the different rooms available their cost, mess and it's menu and all the major points to be noted, entry exit timings, if they are allowed to order food ,kettles etc. Will also mention eateries or other messes nearby we can also add places worth visiting that are nearby 

fests: all fests that happen in that college, their budget and all the events with pictures how many days they last etc

clubs: same as fests and how to enter those clubs 

we can also have a feature of polls and asking questions that everyone can answer  
